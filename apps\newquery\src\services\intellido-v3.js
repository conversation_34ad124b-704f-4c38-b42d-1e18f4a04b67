import { ApiClient } from '@/services/http.ts';
import { useConfig } from '@/hooks/useConfig';

const { serverConfig } = useConfig();

// 临时测试
localStorage.setItem(
    'indchat_token',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjozMywiZXhwIjoxNzUwMzg4ODcyLCJpYXQiOjE3NDc3OTY4NzJ9.Jh7v0LQxZHfUqFg5-mx3eq7Q-UzLFmigu8LCPf4mIQE'
);

const apiClientInstance = new ApiClient({
    // baseURL: `${serverConfig.VITE_API_BASE_URL}/intellido`,
    baseURL: 'http://arai.aialign.com.cn/intellido',
    headers: {
        'Content-Type': 'multipart/form-data',
    },
    tokenKey: 'indchat_token',
});

// 获取全部卡片
export const getAllCards = async () => {
    return apiClientInstance.get('/api/cards/all', {});
};

export const getCardData = async (card_id) => {
    return apiClientInstance.get(`/api/cards/${card_id}`, {});
};

// 获取全部app
export const getAllApps = async () => {
    return apiClientInstance.get('/api/apps/all', {});
};

// 发送聊天
// stream：0正常请求  1流模式请求
// export const sendTaskmessage = async (content, stream, qid, topic_id, stream_out_type="object") => {
//     if (stream) {

//         const token = localStorage.getItem('console_token');
//         const formData = new FormData();
//         formData.append('content', content);
//         let baseMessageUrl = `${import.meta.env.VITE_API_BASE_URL}/myquery/views/taskmessage-add/?stream=${stream}&stream_out_type=${stream_out_type}`
//         if(qid){
//             baseMessageUrl += `&qid=${qid}`
//         }
//         if(topic_id){
//             baseMessageUrl += `&topic_id=${topic_id}`
//         }
//         const response = await fetch(baseMessageUrl, {
//             method: 'POST',
//             headers: {
//                 'Authorization': `Bearer ${token}`
//             },
//             body: formData,
//             // timeout: 0,  //永不超时
//         });

//         if (!response.ok) {
//             throw new Error('Network response was not ok');
//         }

//         return response.body;
//     } else {
//         const response = await apiClientInstance.post('/views/taskmessage-add/',
//             { content },
//             {
//                 params: { stream, qid, tid }
//             }
//         );
//         return response;
//     }
// };

export const sendTaskmessage = async (
    content,
    app_id,
    topic_id,
    scene_token
) => {
    const token = localStorage.getItem('console_token');
    const formData = new FormData();
    formData.append('content', content);
    if (topic_id) {
        formData.append('topic_id', topic_id);
    }

    if (app_id) {
        formData.append('app_id', app_id);
    }

    if (scene_token) {
        formData.append('scene_token', scene_token);
    }
    const response = await fetch(
        `${serverConfig.VITE_API_BASE_URL}/intellido/api/messages/add`,
        {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
            },
            body: formData,
            timeout: 0, //永不超时
        }
    );

    if (!response.ok) {
        throw new Error('Network response was not ok');
    }
    return response.body;
};

// 获取topic列表 app_id指定应用的id
export const getTopicList = async (app_id, scene_token) => {
    const data = {};
    if (app_id) {
        data['app_id'] = app_id;
    }
    if (scene_token) {
        data['scene_token'] = scene_token;
    }
    return apiClientInstance.get('/api/topics/all', {
        params: data,
    });
};

/**
 * Fetches a list of topics based on provided application ID and/or scene token.
 *
 * @async
 * @function getTopicList
 * @param {string} [app_id] - The application ID to filter topics.
 * @param {string} [scene_token] - The scene token to filter topics.
 * @returns {Promise<Object>} A promise that resolves to the response data containing the list of topics.
 *
 * @example
 * getTopicList('myAppId', 'mySceneToken')
 *   .then(response => console.log(response))
 *   .catch(error => console.error(error));
 */
export const getTopicListByTime = async (app_id, scene_token) => {
    const data = {};
    if (app_id) {
        data['app_id'] = app_id;
    }
    if (scene_token) {
        data['scene_token'] = scene_token;
    }
    return apiClientInstance.get('/api/topics/by-time', {
        params: data,
    });
};

// 获取所有历史聊天记录 tid指定topic的id
export const getTaskmessages = async (topic_id) => {
    return apiClientInstance.get('/api/messages/all', {
        params: { topic_id },
    });
};

// 根据关键词搜索历史聊天记录
export const getTopicListByKeywords = async (app_id, scene_token, keywords) => {
    const data = {
        keywords: keywords,
    };
    if (app_id) {
        data['app_id'] = app_id;
    }
    if (scene_token) {
        data['scene_token'] = scene_token;
    }
    return apiClientInstance.get('/api/topics/search', {
        params: data,
    });
};

// 文件转换接口
export const convertFile = (data) => {
    const token = localStorage.getItem('console_token');
    const encodedToken = encodeURIComponent(token);
    return `${serverConfig.VITE_API_BASE_URL}/intellido/api/files/file-convert?url=${data.url}&token=${encodedToken}`;
};

// 删除应用的指定上下文
export const delAppTopicApi = async (topic_id) => {
    return apiClientInstance.post(`/api/topics/${topic_id}/delete`);
};

// 删除消息
export const delTaskmessageApi = async (message_id) => {
    return apiClientInstance.post('/api/messages/delete', {
        message_id,
    });
};

// 发送评论消息，点赞
export const addMessageCommentApi = async (message_id, vote) => {
    return apiClientInstance.post('/api/messages_comment/add', {
        message_id,
        vote,
    });
};

// 删除评论消息，点踩
export const delMessageCommentApi = async (message_id) => {
    return apiClientInstance.post('/api/messages_comment/delete', {
        message_id,
    });
};

// 获取收藏列表
export const getFavoriteListApi = async (page, count, keywords) => {
    return apiClientInstance.get('/api/favorites/page', {
        params: {
            page: page,
            count: count,
            keywords: keywords,
        },
    });
};

// 添加收藏
export const addFavoriteApi = async (message_id, content, idapp_id) => {
    let params;
    if (idapp_id) {
        params = { message_id, content, idapp_id };
    } else {
        params = { message_id, content };
    }
    return apiClientInstance.post('/api/favorites/add', params);
};

// 删除收藏
export const delFavoriteApi = async (favorite_id) => {
    return apiClientInstance.get(`/api/favorites/${favorite_id}/delete`);
};

// 更新收藏
export const updateFavoriteApi = async (favorite_id, title) => {
    return apiClientInstance.post(`/api/favorites/${favorite_id}/update`, {
        title,
    });
};
