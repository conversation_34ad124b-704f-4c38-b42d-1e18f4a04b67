import axios, {
    AxiosInstance,
    AxiosRequestConfig,
    AxiosResponse,
    CustomParamsSerializer,
    Method,
} from 'axios';
import { stringify } from 'qs';
import { getLocaleOnClient } from '@/i18n';

const defaultConfig: AxiosRequestConfig = {
    // 请求超时时间
    timeout: 100000,
    headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
    },
    // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
    paramsSerializer: {
        serialize: stringify as unknown as CustomParamsSerializer,
    },
};

export type RequestMethods = Extract<
    Method,
    'get' | 'post' | 'put' | 'delete' | 'patch' | 'option' | 'head'
>;

// 添加ApiClient配置接口
export interface ApiClientOptions {
    axiosConfig?: AxiosRequestConfig;
    tokenKey?: string;
}

export class ApiClient {
    constructor(options?: ApiClientOptions) {
        this.config = options?.axiosConfig;
        // 设置token的key，默认为'console_token'
        this.tokenKey = options?.tokenKey || 'console_token';
        console.log('ApiClient constructor - axiosConfig:', this.config);
        console.log('ApiClient constructor - tokenKey:', this.tokenKey);
        this.httpInit();
        this.httpInterceptorsRequest();
        this.httpInterceptorsResponse();
    }

    private readonly config: AxiosRequestConfig;
    // 新增tokenKey属性
    private readonly tokenKey: string;

    private axiosInstance: AxiosInstance;

    private httpInit() {
        const finalConfig = { ...defaultConfig, ...this.config };
        console.log('httpInit - finalConfig:', finalConfig);
        this.axiosInstance = axios.create(finalConfig);
    }

    private httpInterceptorsRequest() {
        this.axiosInstance.interceptors.request.use(
            async (config) => {
                // 使用实例的tokenKey而不是硬编码的'console_token'
                const token = localStorage.getItem(this.tokenKey);
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }

                // 添加语言头
                const locale = getLocaleOnClient();
                config.headers['Accept-Language'] = locale;

                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
    }

    private httpInterceptorsResponse() {
        this.axiosInstance.interceptors.response.use(
            (response) => {
                return response;
            },
            (error) => {
                // 检查是否是403状态码
                if (error.response && error.response.status === 403) {
                    // 清除token
                    // localStorage.removeItem('console_token');
                    // 跳转到登录页面，但如果当前已经在登录页则不重复跳转
                    if (window.location.pathname !== '/nq/login') {
                        window.location.href = '/nq/login';
                    }
                    return Promise.reject(error);
                }
                return Promise.reject(error);
            }
        );
    }

    public request<T>(
        method: RequestMethods,
        url: string,
        param?: AxiosRequestConfig,
        axiosConfig?: AxiosRequestConfig
    ): Promise<T> {
        const config = {
            method,
            url,
            ...param,
            ...axiosConfig,
        } as AxiosRequestConfig;

        // 单独处理自定义请求/响应回调
        return new Promise((resolve, reject) => {
            this.axiosInstance
                .request(config)
                .then((response: undefined) => {
                    resolve(response);
                })
                .catch((error) => {
                    reject(error);
                });
        });
    }

    /** 单独抽离的post工具函数 */
    public post<D, P>(
        url: string,
        data?: D,
        config?: AxiosRequestConfig
    ): Promise<P> {
        return this.request<P>('post', url, config?.params || {}, {
            ...config,
            data,
        });
    }

    /** 单独抽离的get工具函数 */
    public get<T, P>(url: string, config?: AxiosRequestConfig): Promise<P> {
        return this.request<P>('get', url, config?.params || {}, config);
    }

    /** 单独抽离的delete工具函数 */
    public delete<T, P>(url: string, config?: AxiosRequestConfig): Promise<P> {
        return this.request<P>('delete', url, config?.params || {}, config);
    }

    /** 单独抽离的put工具函数 */
    public put<D, P>(
        url: string,
        data?: D,
        config?: AxiosRequestConfig
    ): Promise<P> {
        return this.request<P>('put', url, config?.params || {}, {
            ...config,
            data,
        });
    }
}

export const apiClientInstance = new ApiClient();
